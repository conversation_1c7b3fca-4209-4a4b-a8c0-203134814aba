### 0.1.4 (June 24, 2025)

### Enhancements

- Ability to render depth and normal values (#58) (@asundqui)
- New parameters to change renderer focal distance and aperture angle (#59) (@asundqui)
- GLSL code injection example (#56)
- WebXR example (#50)

### Bug fixes

- Option to disable SparkControls camera roll for touch controls (fix #46) (#60) (@asundqui)
- Fix sign in SH2 coefficient signs improving visual quality (#64)

### 0.1.3 (June 11, 2025)

Fix types export in npm package.

### 0.1.2 (June 10, 2025)

It removes unnecessary dependencies from npm package.

### 0.1.1 (June 10, 2025)

### Bug fixes

- Fix compressed .ply files by gsplat not loading (#34) (@bolopenguin, @asundqui)
- Fix image quality rendering with mostly transparent splats (#36) (@hybridherbst, @@asundqui)
- Fix SplatMesh not rendering when it's a child of an Object3D (#38) (@dmarcos)


### 0.1.0 (June 2, 2025)

First release
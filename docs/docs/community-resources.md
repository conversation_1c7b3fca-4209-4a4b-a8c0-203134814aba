# Community Resources

A collection of resources and examples created by the community. To add your own, please submit a PR to edit this page at [sparkjsdev/spark](https://github.com/sparkjsdev/spark/tree/main/docs/docs/community-resources.md).

## Discord

Join the [Spark Discord](https://discord.gg/W39qmSKemS) to connect with other users and developers.

## React Examples

Spark can be used alongside or within React for declarative scene management, dynamic rendering, and state management between your user interface and the 3D scene. See the following examples for how to use Spark with React.

- [`spark-react-basic`](https://github.com/sparkjsdev/spark-react-basic): A basic example of creating a `<canvas>` and THREE.js scene with Spark.
- [`spark-react-r3f`](https://github.com/sparkjsdev/spark-react-r3f): Use Spark declaratively in React with [React Three Fiber](https://r3f.docs.pmnd.rs).
- [`spark-react-router`](https://github.com/sparkjsdev/spark-react-router): An example of using Spark and React Three Fiber with [React Router](https://reactrouter.com) v7 framework mode with SSR.
- [`spark-react-nextjs`](https://github.com/sparkjsdev/spark-react-nextjs): An example of using Spark and React Three Fiber with Next.js App Router.

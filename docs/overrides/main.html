{% extends "base.html" %}
{% block extrahead %}
  {{ super() }}
  <meta name="description" content="An advanced 3D Gaussian Splatting renderer for THREE.js">
  <!-- Facebook Meta Tags -->
  <meta property="og:url" content="https://sparkjs.dev">
  <meta property="og:type" content="website">
  <meta property="og:title" content="Spark">
  <meta property="og:description" content="An advanced 3D Gaussian Splatting renderer for THREE.js">
  <meta property="og:image" content="https://opengraph.b-cdn.net/production/images/763a8fd8-7dad-4f91-ab27-e36a889c2d54.png?token=3c6AgBfDRclxcs5EBSW5KdHsgL0c5WObagFZHS6Xh9w&height=630&width=1200&expires=33285588022">

  <!-- Twitter Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta property="twitter:domain" content="sparkjs.dev">
  <meta property="twitter:url" content="https://sparkjs.dev">
  <meta name="twitter:title" content="Spark">
  <meta name="twitter:description" content="An advanced 3D Gaussian Splatting renderer for THREE.js">
  <meta name="twitter:image" content="https://opengraph.b-cdn.net/production/images/763a8fd8-7dad-4f91-ab27-e36a889c2d54.png?token=3c6AgBfDRclxcs5EBSW5KdHsgL0c5WObagFZHS6Xh9w&height=630&width=1200&expires=33285588022">
  <!-- Meta Tags Generated via https://www.opengraph.xyz -->
  <style>
    .hero {
      text-align: center;
    }
    .hero h1 {
      font-size: 3rem;
      margin-bottom: 0;
    }
    .hero h2 {
      margin-top: 1rem;
      margin-bottom: 2rem;
    }
    .hero p {
      font-size: 1.2rem;
      margin-bottom: 0rem;
    }

    .hero iframe.hero-image {
      width: 100%;
      max-width: 800px;
      height: 600px;
      border-radius: 15px;
      display: block;
      margin: auto;
      margin-top: 2rem;
    }

    .hero img {
      display: block;
      max-width: 550px;
      margin:auto;
    }

    @media screen and (max-width: 60em) {
      .hero img {
        max-width: 320px;
        margin:auto;
      }
      .hero h2 {
        font-size: 22px;
      }
    }

  </style>
{% endblock %}

---
title: Home
hide:
  - navigation
  - toc
---

<div class="hero">
  <h1><img src="/assets/images/logo-hero.png"/></h1>
  <h2>An advanced 3D Gaussian Splatting renderer for THREE.js</h2>
  <div class="feature-list">
  Integrate in your scene with other meshes and splats, fast rendering on all devices, programmable dynamic splat effects, wide format support (ply, spz, splat, ksplat)
  </div>
  <a href="/docs/" class="md-button md-button--primary">Get started →</a>
  <iframe class="hero-image" src="/examples/hello-world/carousel.html"></iframe>
</div>
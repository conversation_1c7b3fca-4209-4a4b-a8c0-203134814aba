name: Deploy site

on:
  push:
    branches:
      - main  # Or your default branch
    paths:
      - 'docs/**'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.x'

      - name: Install dependencies
        run: npm install

      - name: Install mkdocs
        run: pip install mkdocs-material

      - name: Build Site
        run: npm run site:build

      - name: Generate GitHub App token
        id: generate-token
        uses: tibdex/github-app-token@v2
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.APP_PRIVATE_KEY }}

      - name: Deploy to site gh-pages
        uses: peaceiris/actions-gh-pages@v4
        with:
          external_repository: sparkjsdev/sparkjsdev.github.io
          publish_branch: main
          publish_dir: ./site
          personal_token: ${{ steps.generate-token.outputs.token }}
          commit_message: "Deploy Spark site – ${{ github.sha }}"

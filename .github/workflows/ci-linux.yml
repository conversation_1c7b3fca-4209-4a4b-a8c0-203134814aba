name: Spark CI Linux
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
permissions:
  contents: read
jobs:
  test:
    name: spark
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: ['22.x']
    steps:
      - name: Use Node.js ${{ matrix['node-version'] }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix['node-version'] }}

      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Install dependencies
        run: npm install

      - name: Lint
        run: npm run lint

      - name: Tests
        run: npm run test
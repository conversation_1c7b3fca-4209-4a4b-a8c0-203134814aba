name: Spark CI Windows
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
permissions:
  contents: read
jobs:
  test:
    name: spark
    runs-on: windows-latest
    strategy:
      matrix:
        node-version: ['22.x']
    steps:
      - name: Use Node.js ${{ matrix['node-version'] }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix['node-version'] }}

      - name: Set git to use LF
        run: |
          git config --global core.autocrlf false
          git config --global core.eol lf
      - uses: actions/checkout@v4

      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Install dependencies
        run: npm install

      - name: Lint
        run: npm run lint

      - name: Tests
        run: npm run test
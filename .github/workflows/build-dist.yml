name: Build dist

on:
  push:
    branches:
      - main
    paths:
      - 'src/**'

jobs:
  build-dist:
    runs-on: ubuntu-latest

    steps:
      - name: Generate GitHub App token
        id: generate-token
        uses: tibdex/github-app-token@v2
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.APP_PRIVATE_KEY }}

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ steps.generate-token.outputs.token }}

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.x'

      - name: Install dependencies
        run: npm install

      - name: Build Dist
        run: npm run build

      - name: Commit and push changes
        env:
          TOKEN: ${{ steps.generate-token.outputs.token }}
        run: |
          git config user.name "sparkjsdev[bot]"
          git config user.email "123456789+sparkjsdev[bot]@users.noreply.github.com"

          git add .
          git commit -m "Bump dist build" || echo "No build changes"

          SHA=$(git rev-parse HEAD)
          echo "$SHA" > dist/commit.txt

          git add dist/commit.txt
          git commit -m "Update main CDN URL. (https://cdn.jsdelivr.net/gh/sparkjsdev/spark@$SHA/dist/spark.module.js)"

          git push origin HEAD:main

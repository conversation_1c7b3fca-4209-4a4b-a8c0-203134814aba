{"compilerOptions": {"target": "es2020", "module": "es2020", "lib": ["ES2020", "DOM"], "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "moduleResolution": "bundler", "declaration": true, "emitDeclarationOnly": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "types": ["vite/client", "vite-plugin-glsl/ext"]}, "ts-node": {"experimentalSpecifierResolution": "node", "transpileOnly": true, "esm": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "examples"]}
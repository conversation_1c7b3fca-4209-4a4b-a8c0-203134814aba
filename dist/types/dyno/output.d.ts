import { Dyno } from './base';
import { Gsplat } from './splats';
import { DynoVal, DynoVal<PERSON>, HasDynoOut } from './value';
export declare const outputPackedSplat: (gsplat: DynoVal<typeof Gsplat>) => OutputPackedSplat;
export declare const outputRgba8: (rgba8: DynoVal<"vec4">) => OutputRgba8;
export declare class OutputPackedSplat extends Dyno<{
    gsplat: typeof Gsplat;
}, {
    output: "uvec4";
}> implements HasDynoOut<"uvec4"> {
    constructor({ gsplat }: {
        gsplat?: DynoVal<typeof Gsplat>;
    });
    dynoOut(): DynoValue<"uvec4">;
}
export declare class OutputRgba8 extends Dyno<{
    rgba8: "vec4";
}, {
    rgba8: "vec4";
}> implements HasDynoOut<"vec4"> {
    constructor({ rgba8 }: {
        rgba8?: DynoVal<"vec4">;
    });
    dynoOut(): DynoValue<"vec4">;
}

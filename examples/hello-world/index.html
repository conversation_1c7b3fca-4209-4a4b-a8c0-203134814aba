<!DOCTYPE html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Spark • Hello World</title>
  <style>
    body {
      margin: 0;
    }
  </style>
</head>

<body>
  <script type="importmap">
    {
      "imports": {
        "three": "/examples/js/vendor/three/build/three.module.js",
        "@sparkjsdev/spark": "/dist/spark.module.js"
      }
    }
  </script>
  <script type="module">
    import * as THREE from "three";
    import { SplatMesh, SparkRenderer } from "@sparkjsdev/spark";
    import { getAssetFileURL } from "/examples/js/get-asset-url.js";

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer();
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.body.appendChild(renderer.domElement)

    // const spark = new SparkRenderer({ renderer });
    // camera.add(spark)

    //const splatURL = await getAssetFileURL("point_cloud_explicit_SH0.spz");
    const butterfly = new SplatMesh({ url: "https://unicity3dev-res.bdnrc.org.cn/3dgs-unicity/2025-05-14/GlbModel/point_cloud_explicit_SH0.spz" });
    // butterfly.quaternion.set(1, 0, 0, 0);
    // butterfly.position.set(0, 0, -3);
    scene.add(butterfly);

    // renderer.setAnimationLoop(function animate(time) {
    //   renderer.render(scene, camera);
    //   butterfly.rotation.y += 0.01;
    // });
  </script>
</body>

</html>

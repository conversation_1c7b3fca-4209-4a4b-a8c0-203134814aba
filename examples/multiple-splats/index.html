<!DOCTYPE html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Spark • Multiple Splats</title>
  <style>
    body {
      margin: 0;
    }

    a,
    footer {
      color: #797979
    }

    footer {
      position: absolute;
      margin: 10px;
      bottom: 0;
    }
  </style>
</head>

<body>
  <script type="importmap">
    {
      "imports": {
        "three": "/examples/js/vendor/three/build/three.module.js",
        "three/addons/": "/examples/js/vendor/three/examples/jsm/",
        "@sparkjsdev/spark": "/dist/spark.module.js"
      }
    }
  </script>
  <script type="module">
    import * as THREE from "three";
    import { SplatMesh } from "@sparkjsdev/spark";
    import { EXRLoader } from "three/addons/loaders/EXRLoader.js";
    import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
    import { getAssetFileURL } from "/examples/js/get-asset-url.js";

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 0, 6.5);
    camera.lookAt(0, 0, 0);

    const renderer = new THREE.WebGLRenderer();
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(new THREE.Color(0xFFFFFF), 1);
    document.body.appendChild(renderer.domElement)

    const butterflies = [];
    for (let i = 0; i < 6; i++) {
      let splatURL = await getAssetFileURL("butterfly-ai.spz");
      const splat = new SplatMesh({ url: splatURL });
      splat.quaternion.set(1, 0, 0, 0);
      scene.add(splat);
      butterflies.push(splat);
    }

    let splatURL = await getAssetFileURL("cat.spz");
    const cat = new SplatMesh({ url: splatURL });
    cat.quaternion.set(1, 0, 0, 0);
    cat.scale.setScalar(0.5);
    scene.add(cat);

    // Setup mouse controls to orbit the camera around
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.target.set(0, 0, 0);
    controls.minDistance = 0.3;
    controls.maxDistance = 20;
    controls.update();

    const RADIUS = 2;

    renderer.setAnimationLoop(function animate(time) {
      controls.update();

      // Rotate food
      for (let i = 0; i < butterflies.length; i++) {
        const ang = (-time / 10000) + i / butterflies.length * Math.PI * 2;
        butterflies[i].position.set(Math.cos(ang) * RADIUS, Math.sin(ang) * RADIUS - 0.2, 0);
        butterflies[i].rotation.y = i + time / 4000;
      }

      // Animate food master
      cat.position.y = -0.8 + Math.sin(time / 1000) * 0.1;

      renderer.render(scene, camera);
    });
  </script>
</body>

</html>

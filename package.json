{"name": "@sparkjsdev/spark", "version": "0.1.4", "description": "An advanced 3D Gaussian Splatting renderer for THREE.js", "homepage": "https://sparkjs.dev/", "type": "module", "main": "./dist/spark.cjs.js", "module": "./dist/spark.module.js", "types": "./dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/spark.module.js", "require": "./dist/spark.cjs.js"}}, "scripts": {"assets:clean": "node scripts/clean-assets.js", "assets:compress": "node scripts/compress-to-spz.js", "assets:download": "node scripts/download-assets.js", "build": "npm run build:production && npm run build:dev", "build:dev": "vite build --mode dev", "build:production": "vite build --mode production", "build:wasm": "node rust/build_wasm.js", "build:watch": "onchange 'src/**/*.{ts,glsl}' -- npm run build:dev", "clean": "rm -rf dist/ && rm -rf node_modules/ && rm -rf rust/target/ && rm -rf rust/spark-internal-rs/pkg/ && npm run assets:clean", "dev": "npm run build:dev && (vite --host & npm run build:watch)", "docs": "node scripts/clean-site-files.js && node scripts/copy-site-files.js && mkdocs serve", "format": "biome format .", "format:fix": "biome format . --fix", "lint": "biome check .", "lint:fix": "biome check . --fix", "lint:summary": "biome check --reporter=summary", "prepare": "npm run build:wasm && lefthook install", "site:build": "npm run build && npm run site:clean && node scripts/copy-site-files.js && mkdocs build && node scripts/rename-assets-to-static.js", "site:clean": "node scripts/clean-site-files.js && node scripts/clean-site.js", "site:deploy": "npm run site:build && node scripts/deploy-site.js", "site:serve": "node scripts/serve-site.js site", "start": "npm run dev", "test": "node --no-warnings --loader ts-node/esm --test test/**/*.test.ts"}, "repository": "sparkjs-dev/spark", "files": ["dist"], "license": "MIT", "devDependencies": {"@biomejs/biome": "1.9.4", "@types/three": "0.172.0", "lefthook": "1.11.12", "lil-gui": "^0.20.0", "onchange": "7.1.0", "stats.js": "^0.17.0", "three": "^0.172.0", "ts-node": "10.9.2", "typescript": "^5.7.3", "vite": "^6.0.11", "vite-plugin-dts": "^4.5.4", "vite-plugin-glsl": "^1.3.1", "spark-internal-rs": "file:rust/spark-internal-rs/pkg"}, "dependencies": {"fflate": "^0.8.2"}, "keywords": ["3d", "three.js", "gsplats", "3dgs", "gaussian", "splats"]}
{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["dist/*", "examples/showcase/hello-gsplat", "rust/target", "rust/spark-internal-rs/pkg", "src/vrButton.ts", "site", "site-repo", "docs"]}, "formatter": {"enabled": true, "indentStyle": "space", "lineEnding": "lf"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true}}, "javascript": {"formatter": {"quoteStyle": "double"}}}
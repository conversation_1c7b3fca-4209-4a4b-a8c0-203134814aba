site_name: Spark
site_url: https://sparkjs.dev/
theme:
  name: material
  favicon: assets/images/favicon.png
  custom_dir: docs/overrides
  features:
      - content.code.copy
extra_css:
  - stylesheets/spark.css
nav:
  - Home: index.md
  - Getting Started: docs/index.md
  - Overview: docs/overview.md
  - System Design: docs/system-design.md
  - SparkRenderer: docs/spark-renderer.md
  - SparkViewpoint: docs/spark-viewpoint.md
  - SplatMesh: docs/splat-mesh.md
  - PackedSplats: docs/packed-splats.md
  - Loading Gsplats: docs/loading-splats.md
  - Procedural Splats: docs/procedural-splats.md
  - Splat RGBA-XYZ SDF editing: docs/splat-editing.md
  - Dyno overview: docs/dyno-overview.md
  - Dyno standard library: docs/dyno-stdlib.md
  - Controls: docs/controls.md
  - Performance tuning: docs/performance.md
  - Community Resources: docs/community-resources.md
markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences

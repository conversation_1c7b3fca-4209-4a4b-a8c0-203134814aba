// TODO:
// if, switch, for, comment,
// arrayIndex, arrayLength,

export const dynoIf = () => {
  throw new Error("Not implemented");
};
export const dynoSwitch = () => {
  throw new Error("Not implemented");
};
export const dynoFor = () => {
  throw new Error("Not implemented");
};
export const comment = () => {
  throw new Error("Not implemented");
};
export const arrayIndex = () => {
  throw new Error("Not implemented");
};
export const arrayLength = () => {
  throw new Error("Not implemented");
};
